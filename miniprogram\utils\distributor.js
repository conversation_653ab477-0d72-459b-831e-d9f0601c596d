/**
 * 分销员相关工具函数
 */

/**
 * 获取当前分销员信息
 */
function getDistributorInfo() {
  const app = getApp()
  return app.getDistributorInfo()
}

/**
 * 检查是否有分销员信息
 */
function hasDistributorInfo() {
  const distributorInfo = getDistributorInfo()
  return distributorInfo && distributorInfo.code
}

/**
 * 获取分销员编号
 */
function getDistributorCode() {
  const distributorInfo = getDistributorInfo()
  return distributorInfo ? distributorInfo.code : null
}

/**
 * 检查分销员信息是否有效
 */
function isDistributorInfoValid(expireHours) {
  const app = getApp()
  return app.isDistributorInfoValid(expireHours)
}

/**
 * 清除分销员信息
 */
function clearDistributorInfo() {
  const app = getApp()
  app.clearDistributorInfo()
}

/**
 * 在订单中使用分销员信息
 */
function getOrderDistributorParams() {
  const distributorInfo = getDistributorInfo()
  if (!distributorInfo || !isDistributorInfoValid()) {
    return {}
  }
  
  return {
    distributorCode: distributorInfo.code,
    distributorTimestamp: distributorInfo.timestamp
  }
}

/**
 * 显示分销员信息（调试用）
 */
function showDistributorInfo() {
  const distributorInfo = getDistributorInfo()
  if (distributorInfo) {
    wx.showModal({
      title: '分销员信息',
      content: `编号: ${distributorInfo.code}\n时间: ${new Date(distributorInfo.timestamp).toLocaleString()}`,
      showCancel: false
    })
  } else {
    wx.showToast({
      title: '无分销员信息',
      icon: 'none'
    })
  }
}

module.exports = {
  getDistributorInfo,
  hasDistributorInfo,
  getDistributorCode,
  isDistributorInfoValid,
  clearDistributorInfo,
  getOrderDistributorParams,
  showDistributorInfo
}
