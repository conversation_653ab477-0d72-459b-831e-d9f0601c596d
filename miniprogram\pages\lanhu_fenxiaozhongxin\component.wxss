.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.box_1 {
  background-image: linear-gradient(156deg, rgba(250,225,208,1.000000) 0, rgba(205,143,95,1.000000) 100.000000%);
  height: 526rpx;
  width: 750rpx;
  position: relative;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.box_2 {
  width: 402rpx;
  height: 44rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 120rpx 0 0 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 6rpx;
}
.text_1 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.box_3 {
  width: 664rpx;
  height: 86rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 42rpx 0 0 54rpx;
}
.image-text_1 {
  width: 270rpx;
  height: 80rpx;
  margin-top: 4rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.label_1 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.text-group_1 {
  width: 158rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 30rpx;
  margin-left: 20rpx;
}
.text-group_2 {
  width: 168rpx;
  height: 86rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text-wrapper_1 {
  width: 86rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 42rpx;
}
.text_2 {
  width: 86rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 48rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_3 {
  width: 86rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_4 {
  width: 168rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 18rpx;
}
.text-wrapper_2 {
  width: 120rpx;
  height: 42rpx;
  display: flex;
  flex-direction: row;
  margin: 444rpx 0 240rpx 60rpx;
}
.text_5 {
  width: 120rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.box_4 {
  background-color: rgba(31,31,31,1.000000);
  border-radius: 16rpx;
  height: 332rpx;
  width: 690rpx;
  position: absolute;
  left: 30rpx;
  top: 390rpx;
  display: flex;
  flex-direction: column;
}
.section_1 {
  width: 626rpx;
  height: 126rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 52rpx;
}
.text-wrapper_3 {
  width: 194rpx;
  height: 110rpx;
  margin-top: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_6 {
  width: 194rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,0.600000);
  font-size: 28rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_7 {
  width: 142rpx;
  height: 62rpx;
  overflow-wrap: break-word;
  color: rgba(227,205,150,1.000000);
  font-size: 44rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
  margin-top: 8rpx;
  margin-left: -20rpx;
}
.text-wrapper_4 {
  background-color: rgba(227,205,150,1.000000);
  border-radius: 200rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 164rpx;
}
.text_8 {
  width: 56rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 16rpx 0 0 54rpx;
}
.section_2 {
  width: 606rpx;
  height: 102rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 26rpx 0 48rpx 48rpx;
}
.text-wrapper_5 {
  width: 138rpx;
  height: 102rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_9 {
  width: 138rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(249,198,38,1.000000);
  font-size: 28rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_10 {
  width: 96rpx;
  height: 62rpx;
  overflow-wrap: break-word;
  color: rgba(227,205,150,1.000000);
  font-size: 44rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.text-group_3 {
  width: 138rpx;
  height: 102rpx;
  margin-left: 96rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_11 {
  width: 138rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(249,198,38,1.000000);
  font-size: 28rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_12 {
  width: 80rpx;
  height: 62rpx;
  overflow-wrap: break-word;
  color: rgba(227,205,150,1.000000);
  font-size: 44rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 44rpx;
}
.text-group_4 {
  width: 138rpx;
  height: 102rpx;
  margin-left: 96rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.text_13 {
  width: 138rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(249,198,38,1.000000);
  font-size: 28rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.text_14 {
  width: 80rpx;
  height: 62rpx;
  overflow-wrap: break-word;
  color: rgba(227,205,150,1.000000);
  font-size: 44rpx;
  letter-spacing: 0rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}
.box_5 {
  width: 750rpx;
  height: 540rpx;
  display: flex;
  flex-direction: column;
  margin: 560rpx 0 2rpx 0;
}
.section_3 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 518rpx 0 0 232rpx;
}
.box_6 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  height: 300rpx;
  width: 690rpx;
  position: absolute;
  left: 30rpx;
  top: 760rpx;
  display: flex;
  flex-direction: column;
}
.section_4 {
  width: 638rpx;
  height: 52rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 50rpx 0 0 34rpx;
}
.image-text_2 {
  width: 186rpx;
  height: 42rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.thumbnail_4 {
  width: 38rpx;
  height: 34rpx;
  margin-top: 4rpx;
}
.text-group_5 {
  width: 120rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.thumbnail_5 {
  width: 30rpx;
  height: 16rpx;
}
.section_5 {
  width: 638rpx;
  height: 52rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 32rpx 0 0 34rpx;
}
.image-text_3 {
  width: 186rpx;
  height: 42rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.thumbnail_6 {
  width: 38rpx;
  height: 30rpx;
  margin-top: 6rpx;
}
.text-group_6 {
  width: 120rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.thumbnail_7 {
  width: 30rpx;
  height: 16rpx;
}
.section_6 {
  width: 636rpx;
  height: 52rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 32rpx 0 0 36rpx;
}
.image-text_4 {
  width: 184rpx;
  height: 42rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.thumbnail_8 {
  width: 34rpx;
  height: 36rpx;
  margin-top: 2rpx;
}
.text-group_7 {
  width: 120rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.thumbnail_9 {
  width: 30rpx;
  height: 16rpx;
}
.section_7 {
  width: 632rpx;
  height: 52rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 32rpx 0 50rpx 40rpx;
}
.image-text_5 {
  width: 270rpx;
  height: 42rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.thumbnail_10 {
  width: 32rpx;
  height: 32rpx;
  margin-top: 6rpx;
}
.text-group_8 {
  width: 210rpx;
  height: 42rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 30rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 30rpx;
}
.thumbnail_11 {
  width: 30rpx;
  height: 16rpx;
}