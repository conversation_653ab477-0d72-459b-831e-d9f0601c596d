// 引入服务
const httpService = require('../../utils/httpService');
const scenicService = require('../../utils/scenicService');
const userService = require('../../utils/userService');

Page({
  data: {
    // 筛选标签
    filterTabs: [
      { id: 'all', name: '全部', active: true },
      { id: 'pending', name: '待付款', active: false },
      { id: 'paid', name: '已付款', active: false },
      { id: 'verified', name: '已核销', active: false },
      { id: 'cancelled', name: '已取消', active: false }
    ],

    // 当前选中的筛选类型
    currentFilter: 'all',

    // 蓝色指示条位置
    indicatorLeft: 0,

    // 加载状态
    loading: false,
    error: false,
    errorMessage: '',

    // 原始分销订单列表（用于筛选）
    allDistributionOrders: [],

    // 当前显示的分销订单列表
    distributionOrders: [],

    // 用户信息
    userInfo: null
  },

  onLoad: function(options) {
    console.log('分销订单页面加载');
    // 初始化指示条位置
    this.updateIndicatorPosition('all');
    // 加载用户信息和分销订单
    this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      // 获取全局用户信息
      const app = getApp();
      let userInfo = app.globalData.userInfo;

      if (!userInfo) {
        // 如果全局没有用户信息，尝试从本地获取
        userInfo = await userService.getUserInfo();
      }

      if (!userInfo || !userInfo.id) {
        throw new Error('用户未登录');
      }

      this.setData({ userInfo });
      console.log('用户信息加载成功:', userInfo);

      // 加载分销订单
      await this.loadDistributionOrders(userInfo.id);
    } catch (error) {
      console.error('加载用户信息失败:', error);
      this.setData({
        error: true,
        errorMessage: '获取用户信息失败，请重新登录'
      });
    }
  },

  // 加载分销订单
  async loadDistributionOrders(distributorId) {
    try {
      this.setData({ loading: true, error: false });

      console.log('开始加载分销订单，分销员ID:', distributorId);

      // 调用分销订单API
      const response = await httpService.get(`/api/distribution/order/distributor/${distributorId}`, {}, {
        loadingText: '加载订单中...'
      });

      console.log('分销订单API响应:', response);

      let distributionOrders = [];
      if (response && response.data && Array.isArray(response.data)) {
        distributionOrders = response.data;
      } else if (response && Array.isArray(response)) {
        distributionOrders = response;
      }

      console.log('原始分销订单数据:', distributionOrders);

      // 处理每个订单的详细信息
      const processedOrders = await this.processDistributionOrders(distributionOrders);

      this.setData({
        allDistributionOrders: processedOrders,
        distributionOrders: processedOrders,
        loading: false
      });

      console.log('分销订单加载完成，总数:', processedOrders.length);
    } catch (error) {
      console.error('加载分销订单失败:', error);
      this.setData({
        loading: false,
        error: true,
        errorMessage: '加载订单失败，请稍后重试'
      });
    }
  },

  // 处理分销订单数据
  async processDistributionOrders(distributionOrders) {
    const processedOrders = [];

    for (const order of distributionOrders) {
      try {
        console.log('处理订单:', order.orderId);

        // 获取完整订单信息
        const orderDetail = await this.getOrderDetail(order.orderId);
        // 获取产品信息
        const products = await this.getOrderProducts(order.orderId);
        // 获取卡券信息
        const coupons = await this.getOrderCoupons(order.orderId);

        // 处理产品信息
        let productInfo = null;
        if (products && products.length > 0) {
          const product = products[0]; // 取第一个产品
          productInfo = await this.processProductInfo(product);
        }

        // 处理卡券信息（获取核销时间）
        let verifyTime = '未核销';
        let showVerifyTime = false;
        if (coupons && coupons.length > 0) {
          // 检查是否有已使用的卡券
          const usedCoupon = coupons.find(coupon => coupon.usedAt);
          if (usedCoupon) {
            verifyTime = this.formatDateTime(usedCoupon.usedAt);
            showVerifyTime = true;
          } else {
            // 检查是否有已过期的卡券（已取消订单）
            const expiredCoupon = coupons.find(coupon => coupon.status === 'expired');
            if (expiredCoupon) {
              verifyTime = '已取消';
            }
          }
        }

        // 映射状态
        const statusInfo = this.mapOrderStatus(order.status, coupons);

        // 构建处理后的订单数据
        const processedOrder = {
          id: order.id,
          orderNo: orderDetail && orderDetail.orderNo ? orderDetail.orderNo : order.orderId.toString(), // 优先使用订单详情中的订单号
          status: statusInfo.status,
          statusText: statusInfo.statusText,
          productName: productInfo ? productInfo.name : '未知产品',
          productImage: productInfo ? productInfo.image : '../../images/default-product.png',
          orderTime: this.formatDateTime(order.createdAt),
          verifyTime: verifyTime,
          commission: order.commissionAmount || 0,
          showVerifyTime: showVerifyTime,
          originalOrder: order, // 保留原始订单数据
          orderDetail: orderDetail, // 保留订单详情
          coupons: coupons // 保留卡券信息
        };

        processedOrders.push(processedOrder);
        console.log('订单处理完成:', processedOrder);
      } catch (error) {
        console.error('处理订单失败:', order.orderId, error);
        // 即使处理失败也要添加基本信息
        processedOrders.push({
          id: order.id,
          orderNo: order.orderId.toString(),
          status: this.mapOrderStatus(order.status).status,
          statusText: this.mapOrderStatus(order.status).statusText,
          productName: '加载失败',
          productImage: '../../images/default-product.png',
          orderTime: this.formatDateTime(order.createdAt),
          verifyTime: '未知',
          commission: order.commissionAmount || 0,
          showVerifyTime: false,
          originalOrder: order
        });
      }
    }

    return processedOrders;
  },

  // 获取订单详情
  async getOrderDetail(orderId) {
    try {
      const response = await httpService.get(`/api/orders/${orderId}`, {}, {
        showLoading: false
      });

      if (response && response.data) {
        return response.data;
      } else if (response) {
        return response;
      }
      return null;
    } catch (error) {
      console.error('获取订单详情失败:', orderId, error);
      return null;
    }
  },

  // 获取订单产品信息
  async getOrderProducts(orderId) {
    try {
      const response = await httpService.get(`/api/products/order/${orderId}`, {}, {
        showLoading: false
      });

      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response && Array.isArray(response)) {
        return response;
      }
      return [];
    } catch (error) {
      console.error('获取订单产品失败:', orderId, error);
      return [];
    }
  },

  // 获取订单卡券信息
  async getOrderCoupons(orderId) {
    try {
      const response = await httpService.get(`/api/coupons/order/${orderId}`, {}, {
        showLoading: false
      });

      if (response && response.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response && Array.isArray(response)) {
        return response;
      }
      return [];
    } catch (error) {
      console.error('获取订单卡券失败:', orderId, error);
      return [];
    }
  },

  // 处理产品信息
  async processProductInfo(product) {
    try {
      let productName = product.name || '未知产品';
      let productImage = '../../images/default-product.png';

      // 如果是组合包，添加标识
      if (product.productType === 'bundle') {
        productName = `${productName}（组合包）`;
      }

      // 限制产品名称长度
      if (productName.length > 7) {
        productName = productName.substring(0, 7) + '...';
      }

      // 获取景区信息（用于获取图片）
      if (product.scenicId) {
        try {
          // 对于组合包，scenicId可能是逗号分隔的字符串，取第一个
          const firstScenicId = product.scenicId.split(',')[0].trim();
          const scenicDetail = await scenicService.getScenicDetail(firstScenicId);

          if (scenicDetail && scenicDetail.image) {
            productImage = scenicDetail.image;
          } else if (scenicDetail && scenicDetail.images) {
            // 如果images是JSON字符串，尝试解析
            try {
              const imageList = typeof scenicDetail.images === 'string'
                ? JSON.parse(scenicDetail.images)
                : scenicDetail.images;
              if (Array.isArray(imageList) && imageList.length > 0) {
                productImage = imageList[0];
              }
            } catch (parseError) {
              console.warn('解析景区图片失败:', parseError);
            }
          }
        } catch (scenicError) {
          console.warn('获取景区信息失败:', scenicError);
        }
      }

      return {
        name: productName,
        image: productImage,
        originalProduct: product
      };
    } catch (error) {
      console.error('处理产品信息失败:', error);
      return {
        name: '处理失败',
        image: '../../images/default-product.png',
        originalProduct: product
      };
    }
  },

  // 映射订单状态
  mapOrderStatus(apiStatus, coupons = []) {
    // 根据分销订单状态和卡券状态综合判断显示状态
    if (apiStatus === 'canceled') {
      return { status: 'cancelled', statusText: '已取消' };
    }

    if (apiStatus === 'settled') {
      return { status: 'verified', statusText: '已结算' };
    }

    if (apiStatus === 'pending') {
      // 待结算状态下，需要根据卡券状态进一步判断
      if (coupons && coupons.length > 0) {
        // 检查是否有已使用的卡券（已核销）
        const hasUsedCoupon = coupons.some(coupon => coupon.usedAt);
        if (hasUsedCoupon) {
          return { status: 'verified', statusText: '已核销' };
        }

        // 检查是否有已过期的卡券（已取消）
        const hasExpiredCoupon = coupons.some(coupon => coupon.status === 'expired');
        if (hasExpiredCoupon) {
          return { status: 'cancelled', statusText: '已取消' };
        }

        // 检查是否有激活的卡券（已付款）
        const hasActiveCoupon = coupons.some(coupon => coupon.status === 'active');
        if (hasActiveCoupon) {
          return { status: 'paid', statusText: '已付款' };
        }
      }

      return { status: 'pending', statusText: '待结算' };
    }

    return { status: 'pending', statusText: '未知状态' };
  },

  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';

    try {
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      console.error('日期格式化失败:', error);
      return dateTimeStr;
    }
  },

  // 切换筛选标签
  onTabClick: function(e) {
    const tabId = e.currentTarget.dataset.id;
    const tabs = this.data.filterTabs.map(tab => ({
      ...tab,
      active: tab.id === tabId
    }));

    this.setData({
      filterTabs: tabs,
      currentFilter: tabId
    });

    // 更新指示条位置
    this.updateIndicatorPosition(tabId);

    // 筛选订单
    this.filterOrders(tabId);
  },

  // 筛选订单
  filterOrders: function(tabId) {
    let filteredOrders = [];

    switch(tabId) {
      case 'all':
        filteredOrders = this.data.allDistributionOrders;
        break;
      case 'pending':
        // 待付款：目前不存在此类订单，显示数量为 0
        filteredOrders = [];
        break;
      case 'paid':
        // 已付款：显示核销时间为"未核销"的订单（即 verifyTime 为"未核销"的订单）
        filteredOrders = this.data.allDistributionOrders.filter(order =>
          order.status === 'paid' || (order.verifyTime === '未核销' && order.status !== 'cancelled')
        );
        break;
      case 'verified':
        // 已核销：显示核销时间有具体时间的订单（即 verifyTime 有值且不是"未核销"和"已取消"的订单）
        filteredOrders = this.data.allDistributionOrders.filter(order =>
          order.status === 'verified' || (order.verifyTime !== '未核销' && order.verifyTime !== '已取消')
        );
        break;
      case 'cancelled':
        // 已取消：显示门票状态为 expired 的订单，核销时间显示为"已取消"
        filteredOrders = this.data.allDistributionOrders.filter(order =>
          order.status === 'cancelled' || order.verifyTime === '已取消'
        );
        break;
      default:
        filteredOrders = this.data.allDistributionOrders;
    }

    this.setData({
      distributionOrders: filteredOrders
    });

    console.log('筛选订单:', tabId, '结果数量:', filteredOrders.length);
  },

  // 更新指示条位置
  updateIndicatorPosition: function(tabId) {
    const tabIndex = this.data.filterTabs.findIndex(tab => tab.id === tabId);
    const indicatorLeft = tabIndex * 140;

    this.setData({
      indicatorLeft: indicatorLeft
    });
  },

  // 点击订单项
  onOrderClick: function(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('点击订单:', orderId);
    // 这里可以跳转到订单详情页面
    // wx.navigateTo({
    //   url: `/pages/order-detail/index?id=${orderId}`
    // });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    console.log('下拉刷新分销订单');
    if (this.data.userInfo && this.data.userInfo.id) {
      this.loadDistributionOrders(this.data.userInfo.id).finally(() => {
        wx.stopPullDownRefresh();
      });
    } else {
      wx.stopPullDownRefresh();
    }
  },

  // 重新加载数据
  onRetry: function() {
    if (this.data.userInfo && this.data.userInfo.id) {
      this.loadDistributionOrders(this.data.userInfo.id);
    } else {
      this.loadUserInfo();
    }
  },

  // 页面显示时刷新数据
  onShow: function() {
    // 如果页面已经加载过数据，在显示时刷新
    if (this.data.userInfo && this.data.userInfo.id && this.data.allDistributionOrders.length > 0) {
      console.log('页面显示，刷新分销订单数据');
      this.loadDistributionOrders(this.data.userInfo.id);
    }
  }
})
