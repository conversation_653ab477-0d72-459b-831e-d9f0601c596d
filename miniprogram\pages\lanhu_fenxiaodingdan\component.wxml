<view class="page">
  <!-- 固定顶部导航栏 -->
  <view class="nav-bar-fixed">
    <view class="nav-tabs">
      <!-- 全部 -->
      <view class="nav-tab {{filterTabs[0].active ? 'nav-tab-active' : ''}}" data-id="all" bindtap="onTabClick">
        <text class="nav-tab-text {{filterTabs[0].active ? 'nav-tab-text-active' : ''}}">{{filterTabs[0].name}}</text>
      </view>
      <!-- 待付款 -->
      <view class="nav-tab {{filterTabs[1].active ? 'nav-tab-active' : ''}}" data-id="pending" bindtap="onTabClick">
        <text class="nav-tab-text {{filterTabs[1].active ? 'nav-tab-text-active' : ''}}">{{filterTabs[1].name}}</text>
      </view>
      <!-- 已付款 -->
      <view class="nav-tab {{filterTabs[2].active ? 'nav-tab-active' : ''}}" data-id="paid" bindtap="onTabClick">
        <text class="nav-tab-text {{filterTabs[2].active ? 'nav-tab-text-active' : ''}}">{{filterTabs[2].name}}</text>
      </view>
      <!-- 已核销 -->
      <view class="nav-tab {{filterTabs[3].active ? 'nav-tab-active' : ''}}" data-id="verified" bindtap="onTabClick">
        <text class="nav-tab-text {{filterTabs[3].active ? 'nav-tab-text-active' : ''}}">{{filterTabs[3].name}}</text>
      </view>
      <!-- 已取消/退款 -->
      <view class="nav-tab {{filterTabs[4].active ? 'nav-tab-active' : ''}}" data-id="cancelled" bindtap="onTabClick">
        <text class="nav-tab-text {{filterTabs[4].active ? 'nav-tab-text-active' : ''}}">{{filterTabs[4].name}}</text>
      </view>
    </view>
    <!-- 动态蓝色指示条 -->
    <view class="indicator-bar" style="left: {{indicatorLeft}}rpx;"></view>
  </view>
  <!-- 内容区域，添加顶部间距避免被导航栏遮挡 -->
  <view class="content-area">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 错误状态 -->
    <view wx:elif="{{error}}" class="error-container">
      <view class="error-text">{{errorMessage}}</view>
      <button class="retry-button" bindtap="onRetry">重试</button>
    </view>

    <!-- 订单列表 -->
    <view wx:elif="{{distributionOrders.length > 0}}" class="order-list">
      <view wx:for="{{distributionOrders}}" wx:key="id" class="order-item" data-id="{{item.id}}" bindtap="onOrderClick">
        <view class="order-header">
          <text lines="1" class="order-no">订单号：{{item.orderNo}}</text>
          <text lines="1" class="order-status {{item.status === 'cancelled' ? 'status-cancelled' : ''}}">{{item.statusText}}</text>
        </view>
        <view class="order-content">
          <image src="{{item.productImage}}" class="product-image"></image>
          <view class="product-info">
            <text lines="1" class="product-name">{{item.productName}}</text>
            <text lines="1" decode="true" class="order-time">下单时间：{{item.orderTime}}</text>
            <view class="verify-time-wrapper">
              <text lines="1" class="verify-label">核销时间：</text>
              <text lines="1" class="verify-time {{item.verifyTime === '未核销' ? 'not-verified' : (item.verifyTime === '已取消' ? 'cancelled' : 'verified')}}">{{item.verifyTime}}</text>
            </view>
            <view class="commission-wrapper">
              <text lines="1" class="commission-label">预计佣金：</text>
              <text lines="1" class="commission-symbol">￥</text>
              <text lines="1" class="commission-amount">{{item.commission}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-container">
      <view class="empty-text">暂无分销订单</view>
      <view class="empty-desc">您还没有任何分销订单记录</view>
    </view>
  </view>
</view>