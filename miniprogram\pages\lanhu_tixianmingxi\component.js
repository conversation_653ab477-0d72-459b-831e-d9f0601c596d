const httpService = require('../../utils/httpService');
const userService = require('../../utils/userService');

Component({
  properties: {},
  data: {
    withdrawalRecords: [], // 提现记录列表
    allRecords: [], // 所有记录（用于筛选）
    selectedDate: '', // 选中的日期
    selectedDateText: '', // 显示的日期文本
    currentDate: '', // 当前日期
    showPicker: false, // 是否显示日期选择器
    distributorId: null, // 分销员ID
    loading: false // 加载状态
  },
  lifetimes: {
    created: function () {
      // 初始化当前日期
      const now = new Date();
      const currentDate = this.formatDate(now);
      const currentYearMonth = now.getFullYear() + '年' + (now.getMonth() + 1) + '月';

      this.setData({
        currentDate: currentDate,
        selectedDate: currentDate,
        selectedDateText: currentYearMonth
      });
    },
    attached: function () {
      console.info("提现明细页面加载");
      this.initPage();
    },
    detached: function () {
      console.info("提现明细页面卸载");
    },
  },
  methods: {
    // 初始化页面
    async initPage() {
      try {
        // 获取用户信息
        const userInfo = await userService.getCurrentUser();
        if (!userInfo || !userInfo.id) {
          wx.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }

        // 设置分销员ID（这里假设用户ID就是分销员ID，实际可能需要调整）
        this.setData({
          distributorId: userInfo.id
        });

        // 加载提现记录
        await this.loadWithdrawalRecords();

      } catch (error) {
        console.error('初始化页面失败:', error);
        wx.showToast({
          title: '页面初始化失败',
          icon: 'none'
        });
      }
    },

    // 加载提现记录
    async loadWithdrawalRecords() {
      if (!this.data.distributorId) {
        console.warn('分销员ID为空，无法加载提现记录');
        return;
      }

      try {
        this.setData({ loading: true });

        console.log('开始加载提现记录，分销员ID:', this.data.distributorId);

        const response = await httpService.get(`/api/distribution/withdrawal/distributor/${this.data.distributorId}`, {}, {
          showLoading: true,
          loadingText: '加载提现记录中...'
        });

        let records = [];
        if (response && response.data && Array.isArray(response.data)) {
          records = response.data;
        } else if (response && Array.isArray(response)) {
          records = response;
        }

        console.log('获取到提现记录:', records.length, '条');

        // 处理数据
        const processedRecords = this.processWithdrawalRecords(records);

        this.setData({
          allRecords: processedRecords,
          withdrawalRecords: processedRecords
        });

        // 根据当前选择的日期进行筛选
        this.filterRecordsByDate();

      } catch (error) {
        console.error('加载提现记录失败:', error);
        wx.showToast({
          title: '加载失败，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.setData({ loading: false });
      }
    },
